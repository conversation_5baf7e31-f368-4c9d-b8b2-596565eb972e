# A后端改造 - 主后端API接口文档

## 📋 概述

本文档为A后端（Cloudflare Workers）改造提供主后端API接口规范。通过使用主后端API，A后端可以直接访问用户认证、卡密管理、配额检查等功能，无需维护独立的B后端API系统。

## 🔐 认证机制

### JWT Token认证
所有需要认证的API都使用Bearer Token认证：

```http
Authorization: Bearer <access_token>
```

### 环境变量配置

```javascript
// Cloudflare Workers环境变量
MAIN_BACKEND_BASE=https://your-main-backend.com
JWT_SECRET=your-jwt-secret-key

// B后端API配置（用于配额管理）
B_BACKEND_API_TOKEN=B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6
```

## 🌐 基础请求函数

```javascript
// 主后端API调用函数
async function callMainBackendApi(endpoint, data = null, token = null) {
  const url = `${env.MAIN_BACKEND_BASE}/api${endpoint}`;
  const options = {
    method: data ? 'POST' : 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return await response.json();
}

// B后端API调用函数（用于配额管理）
async function callBBackendApi(endpoint, data = null, env) {
  const url = `${env.MAIN_BACKEND_BASE}/api/b-backend${endpoint}`;
  const options = {
    method: data ? 'POST' : 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${env.B_BACKEND_API_TOKEN}`
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return await response.json();
}
```

## 📋 接口清单

### 1. 用户认证接口

#### 1.1 用户登录
**POST /api/auth/login**

支持用户名或邮箱登录

**请求参数：**
```json
{
  "username": "user123",  // 用户名或邮箱
  "password": "password123"
}
```

**响应示例：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 7200,
  "username": "user123"
}
```

**错误响应：**
```json
{
  "error": "用户名或密码错误",
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

#### 1.2 用户注册（两步验证）

##### 第一步：发送验证码
**POST /api/auth/send-verification**

**请求参数：**
```json
{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>"
}
```

**响应示例：**
```json
{
  "message": "验证码已发送到您的邮箱，请查收",
  "email": "<EMAIL>"
}
```

##### 第二步：验证邮箱并完成注册
**POST /api/auth/verify-email**

**请求参数：**
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "code": "123456"
}
```

**响应示例：**
```json
{
  "message": "注册成功",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 7200,
  "username": "newuser"
}
```

#### 1.3 Token刷新
**POST /api/auth/refresh**

**请求参数：**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 7200
}
```

#### 1.4 Token验证
**GET /api/auth/verify**

**请求头：**
```http
Authorization: Bearer <access_token>
```

**响应示例：**
```json
{
  "valid": true,
  "username": "user123"
}
```

#### 1.5 修改密码
**POST /api/auth/change-password**

**请求头：**
```http
Authorization: Bearer <access_token>
```

**请求参数：**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123"
}
```

**响应示例：**
```json
{
  "message": "密码修改成功"
}
```

#### 1.6 忘记密码（两步验证）

##### 第一步：发送重置验证码
**POST /api/auth/forgot-password**

**请求参数：**
```json
{
  "email": "<EMAIL>"
}
```

**响应示例：**
```json
{
  "message": "重置密码验证码已发送到您的邮箱，请查收",
  "email": "<EMAIL>"
}
```

##### 第二步：重置密码
**POST /api/auth/reset-password**

**请求参数：**
```json
{
  "email": "<EMAIL>",
  "code": "123456",
  "newPassword": "newpassword123"
}
```

**响应示例：**
```json
{
  "message": "密码重置成功，请使用新密码登录"
}
```

### 2. 用户信息接口

#### 2.1 获取用户配额信息
**GET /api/user/quota**

**请求头：**
```http
Authorization: Bearer <access_token>
```

**响应示例：**
```json
{
  "username": "user123",
  "vip": {
    "type": "M",
    "expireAt": 1735689600000,
    "quotaChars": 80000,
    "usedChars": 15000,
    "remainingChars": 65000,
    "usagePercentage": 18.75,
    "isLegacyUser": false,
    "isExpired": false
  },
  "usage": {
    "totalChars": 150000,
    "monthlyChars": 15000,
    "monthlyResetAt": 1753977600000
  }
}
```

#### 2.2 获取用户详细信息
**GET /api/user/profile**

**请求头：**
```http
Authorization: Bearer <access_token>
```

**响应示例：**
```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "vip": {
    "type": "M",
    "expireAt": 1735689600000,
    "quotaChars": 80000,
    "usedChars": 15000,
    "remainingChars": 65000,
    "usagePercentage": 18.75,
    "isLegacyUser": false,
    "isExpired": false
  },
  "usage": {
    "totalChars": 150000,
    "monthlyChars": 15000,
    "monthlyResetAt": 1753977600000
  }
}
```

### 3. 卡密管理接口

#### 3.1 使用卡密激活VIP
**POST /api/card/use**

**请求头：**
```http
Authorization: Bearer <access_token>
```

**请求参数：**
```json
{
  "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q"
}
```

**响应示例：**
```json
{
  "quota": {
    "type": "M",
    "expireAt": 1735689600000,
    "quotaChars": 80000,
    "usedChars": 0,
    "remainingChars": 80000
  }
}
```

**错误响应：**
```json
{
  "error": "卡密无效或已被使用"
}
```

### 4. 配额管理接口（B后端API）

> **重要说明**：主后端目前缺少配额管理的HTTP API接口，但B后端API已经实现了完整的配额管理功能。如果选择使用主后端API方案，建议将这些接口迁移到主后端的 `/api/user` 路由中。

#### 4.1 配额预检查
**POST /api/b-backend/users/check-quota**

用于TTS任务开始前检查用户配额是否足够。

**请求头：**
```http
Authorization: Bearer <B_Backend_API_Token>
```

**请求参数：**
```json
{
  "username": "user123",
  "requiredTier": "STANDARD",  // 可选，默认"STANDARD"，可选值："STANDARD"/"PRO"
  "requestedChars": 1500       // 可选，默认0（不检查配额）
}
```

**成功响应 (200)：**
```json
{
  "success": true,
  "message": "配额检查通过",
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

**配额不足响应 (403)：**
```json
{
  "error": "字符数配额不足。剩余 500 字符，本次需要 1500 字符。请升级或续费套餐。",
  "code": 4003,
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

**权限不足响应 (403)：**
```json
{
  "error": "此功能需要PRO会员权限",
  "code": 4003,
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

**会员过期响应 (403)：**
```json
{
  "error": "会员已过期，请续费",
  "code": 4003,
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

**参数错误响应 (400)：**
```json
{
  "error": "缺少必需参数: username",
  "code": 4000,
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

#### 4.2 配额扣除
**POST /api/b-backend/users/update-usage**

用于TTS任务完成后扣除用户配额。

**请求头：**
```http
Authorization: Bearer <B_Backend_API_Token>
```

**请求参数：**
```json
{
  "username": "user123",
  "charCount": 1450           // 实际使用的字符数
}
```

**成功响应 (200)：**
```json
{
  "success": true,
  "message": "使用量更新成功",
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

**参数错误响应 (400)：**
```json
{
  "error": "charCount必须是非负数",
  "code": 4000,
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

**用户不存在响应 (500)：**
```json
{
  "error": "Internal server error",
  "code": 5000,
  "timestamp": "2025-07-28T12:00:00.000Z"
}
```

## 🔄 A后端函数替换对照表

### 用户认证函数

```javascript
// 旧版本 (KV)
async function verifyToken(token, env) {
  // KV访问逻辑...
}

// 新版本 (主后端API) - 推荐本地验证
async function verifyToken(token, env) {
  // 使用相同的JWT_SECRET进行本地验证，性能更好
  return verifyTokenLocally(token, env.JWT_SECRET);
}

// 或者调用主后端API验证
async function verifyTokenViaAPI(token, env) {
  const result = await callMainBackendApi('/auth/verify', null, token);
  return result.username;
}
```

### 用户登录函数

```javascript
// 新版本 (主后端API)
async function loginUser(username, password, env) {
  return await callMainBackendApi('/auth/login', { username, password });
}
```

### 用户注册函数

```javascript
// 新版本 (主后端API) - 两步验证
async function sendVerificationCode(username, password, email, env) {
  return await callMainBackendApi('/auth/send-verification', { username, password, email });
}

async function verifyEmailAndRegister(username, email, code, env) {
  return await callMainBackendApi('/auth/verify-email', { username, email, code });
}
```

### 卡密激活函数

```javascript
// 旧版本 (KV)
async function useCard(code, username, env) {
  // KV访问逻辑...
}

// 新版本 (主后端API)
async function useCard(code, token, env) {
  return await callMainBackendApi('/card/use', { code }, token);
}
```

### 配额检查函数

```javascript
// 旧版本 (KV)
async function checkVip(username, tier, chars, env) {
  // KV访问逻辑...
}

// 新版本 (主后端API) - 方案1：仅查询配额
async function checkUserQuota(token, env) {
  return await callMainBackendApi('/user/quota', null, token);
}

// 新版本 (B后端API) - 方案2：完整配额预检查
async function checkVipQuota(username, requiredTier, requestedChars, env) {
  return await callBBackendApi('/users/check-quota', {
    username,
    requiredTier,
    requestedChars
  }, env);
}
```

### 配额扣除函数

```javascript
// 旧版本 (KV)
async function updateUserUsage(username, charCount, env) {
  // KV访问逻辑...
}

// 新版本 (B后端API) - 推荐使用
async function updateUserUsage(username, charCount, env) {
  return await callBBackendApi('/users/update-usage', {
    username,
    charCount
  }, env);
}

// 新版本 (主后端API) - 需要先实现接口
async function updateUserUsageViaMain(token, charCount, env) {
  // 需要主后端先实现 POST /api/user/quota/consume 接口
  return await callMainBackendApi('/user/quota/consume', {
    charCount
  }, token);
}
```

## ⚡ 性能优化建议

### 1. Token验证优化
```javascript
// 推荐：本地验证JWT Token（性能最佳）
async function verifyTokenLocally(token, jwtSecret) {
  // 使用相同的JWT验证逻辑
  const [header, payload, signature] = token.split('.');
  // ... JWT验证逻辑
}

// 备选：API验证（网络开销）
async function verifyTokenViaAPI(token, env) {
  return await callMainBackendApi('/auth/verify', null, token);
}
```

### 2. 缓存策略
```javascript
// 实现智能缓存减少API调用
const CACHE_CONFIG = {
  userQuota: { ttl: 300000 }, // 5分钟
  userProfile: { ttl: 600000 }, // 10分钟
};

async function getCachedUserQuota(token, env) {
  const cacheKey = `quota:${token}`;
  let cached = await env.CACHE.get(cacheKey);
  
  if (!cached) {
    const quota = await callMainBackendApi('/user/quota', null, token);
    await env.CACHE.put(cacheKey, JSON.stringify(quota), { expirationTtl: 300 });
    return quota;
  }
  
  return JSON.parse(cached);
}
```

### 3. 错误处理和重试
```javascript
// 实现重试机制
async function callMainBackendApiWithRetry(endpoint, data, token, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await callMainBackendApi(endpoint, data, token);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // 指数退避
    }
  }
}
```

## 🚨 错误码说明

| HTTP状态码 | 错误类型 | 说明 |
|-----------|---------|------|
| 400 | 参数错误 | 请求参数缺失或格式错误 |
| 401 | 认证失败 | Token无效、过期或缺失 |
| 403 | 权限不足 | 用户权限不足 |
| 404 | 资源不存在 | 用户不存在或资源未找到 |
| 409 | 冲突 | 用户名或邮箱已存在 |
| 429 | 请求过频 | 请求频率超限 |
| 500 | 服务器错误 | 内部服务器错误 |

## 🔧 迁移检查清单

### 阶段1：核心功能迁移
- [ ] 用户登录接口替换
- [ ] 用户注册接口替换
- [ ] Token验证逻辑替换
- [ ] 卡密激活接口替换
- [ ] 配额查询接口替换
- [ ] **配额预检查接口替换（B后端API）**
- [ ] **配额扣除接口替换（B后端API）**

### 阶段2：完善和优化
- [ ] 实现错误处理和重试机制
- [ ] 添加缓存策略
- [ ] 实现降级策略
- [ ] 完善日志记录
- [ ] **配额管理异常处理**
- [ ] **TTS任务与配额管理集成**

### 阶段3：测试和验证
- [ ] 功能测试
- [ ] 性能测试
- [ ] 错误场景测试
- [ ] **配额管理功能测试**
- [ ] **老用户兼容性测试**
- [ ] **并发配额扣除测试**
- [ ] 生产环境验证

### 阶段4：架构选择（二选一）
- [ ] **方案A：混合架构** - 主后端API + B后端配额管理
- [ ] **方案B：纯主后端** - 将B后端配额接口迁移到主后端

## 📞 技术支持

如有问题，请参考：
1. 主后端API日志
2. Cloudflare Workers日志
3. 网络连接状态
4. JWT Token有效性

## 🎯 完整迁移示例

### Worker.js 核心函数改造示例

```javascript
// ========== 环境变量配置 ==========
const MAIN_BACKEND_BASE = env.MAIN_BACKEND_BASE; // https://your-main-backend.com
const JWT_SECRET = env.JWT_SECRET;
const B_BACKEND_API_TOKEN = env.B_BACKEND_API_TOKEN;

// ========== 通用API调用函数 ==========
async function callMainBackendApi(endpoint, data = null, token = null) {
  const url = `${MAIN_BACKEND_BASE}/api${endpoint}`;
  const options = {
    method: data ? 'POST' : 'GET',
    headers: { 'Content-Type': 'application/json' }
  };

  if (token) options.headers['Authorization'] = `Bearer ${token}`;
  if (data) options.body = JSON.stringify(data);

  const response = await fetch(url, options);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return await response.json();
}

// ========== B后端API调用函数 ==========
async function callBBackendApi(endpoint, data = null, env) {
  const url = `${MAIN_BACKEND_BASE}/api/b-backend${endpoint}`;
  const options = {
    method: data ? 'POST' : 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${B_BACKEND_API_TOKEN}`
    }
  };

  if (data) options.body = JSON.stringify(data);

  const response = await fetch(url, options);
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return await response.json();
}

// ========== 认证函数改造 ==========

// 1. 用户登录
async function loginUser(username, password, env) {
  try {
    const result = await callMainBackendApi('/auth/login', { username, password });
    return {
      success: true,
      access_token: result.access_token,
      refresh_token: result.refresh_token,
      username: result.username,
      expires_in: result.expires_in
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 2. 用户注册（两步验证）
async function sendRegistrationCode(username, password, email, env) {
  try {
    const result = await callMainBackendApi('/auth/send-verification', { username, password, email });
    return { success: true, message: result.message, email: result.email };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function completeRegistration(username, email, code, env) {
  try {
    const result = await callMainBackendApi('/auth/verify-email', { username, email, code });
    return {
      success: true,
      access_token: result.access_token,
      refresh_token: result.refresh_token,
      username: result.username,
      expires_in: result.expires_in
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 3. Token验证（本地验证，性能最佳）
async function verifyToken(token, env) {
  try {
    const [header, payload, signature] = token.split('.');
    if (!header || !payload || !signature) {
      throw new Error('Invalid token format');
    }

    // 验证签名
    const expectedSignature = btoa(await hmacSha256(`${header}.${payload}`, JWT_SECRET));
    if (signature !== expectedSignature) {
      throw new Error('Invalid signature');
    }

    // 解析payload
    const decoded = JSON.parse(atob(payload));

    // 检查过期时间
    if (Date.now() > decoded.exp) {
      throw new Error('Token expired');
    }

    return decoded.sub; // 返回用户名
  } catch (error) {
    throw new Error('Token verification failed: ' + error.message);
  }
}

// 4. 卡密激活
async function useCard(code, token, env) {
  try {
    const result = await callMainBackendApi('/card/use', { code }, token);
    return {
      success: true,
      quota: result.quota
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 5. 配额检查
async function checkUserQuota(token, env) {
  try {
    const result = await callMainBackendApi('/user/quota', null, token);
    return {
      success: true,
      username: result.username,
      vip: result.vip,
      usage: result.usage
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 6. 密码重置（两步验证）
async function sendPasswordResetCode(email, env) {
  try {
    const result = await callMainBackendApi('/auth/forgot-password', { email });
    return { success: true, message: result.message, email: result.email };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function resetPassword(email, code, newPassword, env) {
  try {
    const result = await callMainBackendApi('/auth/reset-password', { email, code, newPassword });
    return { success: true, message: result.message };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// ========== 配额管理函数改造 ==========

// 7. 配额预检查（推荐使用B后端API）
async function checkVipQuota(username, requiredTier, requestedChars, env) {
  try {
    const result = await callBBackendApi('/users/check-quota', {
      username,
      requiredTier,
      requestedChars
    }, env);
    return { success: true, message: result.message };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 8. 配额扣除（推荐使用B后端API）
async function updateUserUsage(username, charCount, env) {
  try {
    const result = await callBBackendApi('/users/update-usage', {
      username,
      charCount
    }, env);
    return { success: true, message: result.message };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 9. 完整的TTS任务配额管理流程
async function processTTSWithQuota(username, text, requiredTier, env) {
  const charCount = text.length;

  try {
    // 步骤1：预检查配额
    const quotaCheck = await checkVipQuota(username, requiredTier, charCount, env);
    if (!quotaCheck.success) {
      throw new Error(quotaCheck.error);
    }

    // 步骤2：执行TTS任务
    // ... TTS处理逻辑 ...

    // 步骤3：扣除配额
    const usageUpdate = await updateUserUsage(username, charCount, env);
    if (!usageUpdate.success) {
      console.error('配额扣除失败:', usageUpdate.error);
      // 根据业务需求决定是否抛出错误
    }

    return { success: true, message: 'TTS任务完成' };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// ========== 辅助函数 ==========
async function hmacSha256(data, key) {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(key);
  const dataData = encoder.encode(data);

  const cryptoKey = await crypto.subtle.importKey(
    'raw', keyData, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataData);
  return new Uint8Array(signature);
}

function btoa(buffer) {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return globalThis.btoa(binary);
}

function atob(str) {
  return globalThis.atob(str);
}
```

## 🔄 路由处理改造示例

```javascript
// ========== 主要路由处理改造 ==========

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      // 用户登录
      if (path === '/api/login' && request.method === 'POST') {
        const { username, password } = await request.json();
        const result = await loginUser(username, password, env);

        if (result.success) {
          return new Response(JSON.stringify({
            access_token: result.access_token,
            refresh_token: result.refresh_token,
            username: result.username,
            expires_in: result.expires_in
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      // 用户注册 - 第一步
      if (path === '/api/register/send-code' && request.method === 'POST') {
        const { username, password, email } = await request.json();
        const result = await sendRegistrationCode(username, password, email, env);

        return new Response(JSON.stringify(result), {
          status: result.success ? 200 : 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 用户注册 - 第二步
      if (path === '/api/register/verify' && request.method === 'POST') {
        const { username, email, code } = await request.json();
        const result = await completeRegistration(username, email, code, env);

        if (result.success) {
          return new Response(JSON.stringify({
            access_token: result.access_token,
            refresh_token: result.refresh_token,
            username: result.username,
            expires_in: result.expires_in
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } else {
          return new Response(JSON.stringify({ error: result.error }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      // 卡密激活
      if (path === '/api/card/use' && request.method === 'POST') {
        const token = request.headers.get('Authorization')?.replace('Bearer ', '');
        if (!token) {
          return new Response(JSON.stringify({ error: 'Token required' }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        const { code } = await request.json();
        const result = await useCard(code, token, env);

        return new Response(JSON.stringify(result), {
          status: result.success ? 200 : 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 配额查询
      if (path === '/api/user/quota' && request.method === 'GET') {
        const token = request.headers.get('Authorization')?.replace('Bearer ', '');
        if (!token) {
          return new Response(JSON.stringify({ error: 'Token required' }), {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        const result = await checkUserQuota(token, env);

        return new Response(JSON.stringify(result), {
          status: result.success ? 200 : 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 密码重置 - 第一步
      if (path === '/api/password/reset/send-code' && request.method === 'POST') {
        const { email } = await request.json();
        const result = await sendPasswordResetCode(email, env);

        return new Response(JSON.stringify(result), {
          status: result.success ? 200 : 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 密码重置 - 第二步
      if (path === '/api/password/reset/confirm' && request.method === 'POST') {
        const { email, code, newPassword } = await request.json();
        const result = await resetPassword(email, code, newPassword, env);

        return new Response(JSON.stringify(result), {
          status: result.success ? 200 : 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // TTS相关路由保持不变...
      // 其他路由处理...

    } catch (error) {
      console.error('Request processing error:', error);
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
};
```

## 📊 数据结构对照表

### VIP信息结构
```javascript
// 主后端API返回的VIP信息结构
{
  "vip": {
    "type": "M",                    // 套餐类型：M/Q/H/PM/PQ/PH/PT
    "expireAt": 1735689600000,      // 过期时间戳
    "quotaChars": 80000,            // 总配额字符数
    "usedChars": 15000,             // 已使用字符数
    "remainingChars": 65000,        // 剩余字符数
    "usagePercentage": 18.75,       // 使用百分比
    "isLegacyUser": false,          // 是否为老用户
    "isExpired": false              // 是否已过期
  }
}
```

### 套餐类型说明
| 套餐代码 | 套餐名称 | 字符配额 | 有效期 | 权限等级 |
|---------|---------|---------|--------|----------|
| M | 月度标准版 | 80,000 | 30天 | STANDARD |
| Q | 季度标准版 | 250,000 | 90天 | STANDARD |
| H | 半年标准版 | 500,000 | 180天 | STANDARD |
| PM | 月度专业版 | 200,000 | 30天 | PRO |
| PQ | 季度专业版 | 650,000 | 90天 | PRO |
| PH | 半年专业版 | 1,300,000 | 180天 | PRO |
| PT | 测试版 | 1,000 | 1小时 | STANDARD |

## 🛡️ 安全注意事项

### 1. Token安全
```javascript
// 确保JWT_SECRET的安全性
// 生产环境中使用强随机密钥
const JWT_SECRET = env.JWT_SECRET; // 至少32位随机字符

// Token过期时间设置
const ACCESS_TOKEN_EXPIRE = 7200;  // 2小时
const REFRESH_TOKEN_EXPIRE = 604800; // 7天
```

### 2. API调用安全
```javascript
// 实现请求签名验证（可选）
async function signRequest(data, secret) {
  const timestamp = Date.now();
  const payload = JSON.stringify(data) + timestamp;
  const signature = await hmacSha256(payload, secret);
  return { timestamp, signature: btoa(signature) };
}
```

### 3. 错误信息安全
```javascript
// 避免泄露敏感信息
function sanitizeError(error) {
  // 生产环境中隐藏详细错误信息
  if (env.ENVIRONMENT === 'production') {
    return '服务暂时不可用，请稍后重试';
  }
  return error.message;
}
```

## 🎯 配额管理架构选择建议

### 方案对比

| 特性 | 混合架构（主后端+B后端） | 纯主后端架构 |
|------|------------------------|-------------|
| **开发成本** | 低（直接使用现有B后端API） | 中（需要迁移接口） |
| **维护成本** | 中（维护两套API） | 低（单一API系统） |
| **性能** | 好（B后端API已优化） | 好（减少网络跳转） |
| **一致性** | 中（需要同步两套系统） | 高（单一数据源） |
| **推荐度** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

### 推荐实施路径

#### 短期方案（1-2周）：混合架构
```javascript
// 使用现有B后端API进行配额管理
const quotaCheck = await callBBackendApi('/users/check-quota', {
  username, requiredTier, requestedChars
}, env);

const usageUpdate = await callBBackendApi('/users/update-usage', {
  username, charCount
}, env);
```

**优势**：
- ✅ 快速实施，无需开发新接口
- ✅ B后端API已经过充分测试
- ✅ 完整的错误处理和参数验证

#### 长期方案（1个月）：纯主后端架构
```javascript
// 将B后端配额接口迁移到主后端
POST /api/user/quota/check    // 迁移自 /api/b-backend/users/check-quota
POST /api/user/quota/consume  // 迁移自 /api/b-backend/users/update-usage
```

**优势**：
- ✅ 架构更简洁，维护成本更低
- ✅ 数据一致性更好
- ✅ 符合长期架构规划

### 迁移时间线

```mermaid
gantt
    title A后端改造时间线
    dateFormat  YYYY-MM-DD
    section 阶段1：基础功能
    认证接口迁移    :done, auth, 2025-01-01, 3d
    用户管理迁移    :done, user, after auth, 2d
    卡密管理迁移    :done, card, after user, 2d
    section 阶段2：配额管理
    B后端API集成    :active, quota-b, after card, 5d
    配额功能测试    :quota-test, after quota-b, 3d
    section 阶段3：优化
    主后端接口开发  :main-quota, after quota-test, 7d
    接口迁移        :migrate, after main-quota, 3d
    B后端API下线    :cleanup, after migrate, 2d
```

---

**注意：本文档基于主后端API v1.0.0版本编写，请确保API版本兼容性。配额管理功能基于B后端API v1.0.0实现。**
