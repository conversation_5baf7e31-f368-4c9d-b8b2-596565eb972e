# B后端API快速参考卡片

## 🚀 快速开始

### 1. 环境变量配置
```javascript
// Cloudflare Workers环境变量
PG_API_BASE=https://your-backend-domain.com
API_SECRET_TOKEN=B_Backend_API_2024_SecureToken_X9k2#mP8$vL3nQ5@jR7wY4*tZ6
```

### 2. 基础请求函数
```javascript
async function callPgApi(endpoint, data = null) {
  const response = await fetch(`${env.PG_API_BASE}/api/b-backend${endpoint}`, {
    method: data ? 'POST' : 'GET',
    headers: {
      'Authorization': `Bearer ${env.API_SECRET_TOKEN}`,
      'Content-Type': 'application/json'
    },
    body: data ? JSON.stringify(data) : null
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `HTTP ${response.status}`);
  }
  
  return await response.json();
}
```

## 📋 接口速查表

| 接口 | 方法 | 端点 | 用途 |
|------|------|------|------|
| 健康检查 | GET | `/health` | 检查服务状态 |
| Token验证 | POST | `/auth/verify` | 验证JWT Token |
| 用户注册同步 🆕 | POST | `/users/register` | A后端注册用户同步到B后端 |
| 密码重置同步 🆕 | POST | `/users/reset-password` | A后端密码重置同步到B后端 |
| 配额检查 | POST | `/users/check-quota` | 检查用户配额 |
| 使用量更新 | POST | `/users/update-usage` | 更新字符使用量 |

## 🔄 函数替换对照表

### verifyToken函数
```javascript
// 旧版本 (KV)
async function verifyToken(token, env) {
  // KV访问逻辑...
}

// 新版本 (API)
async function verifyToken(token, env) {
  const result = await callPgApi('/auth/verify', { token });
  return result.username;
}
```

### checkVip函数
```javascript
// 旧版本 (KV)
async function checkVip(username, tier, chars, env) {
  // KV访问逻辑...
}

// 新版本 (API)
async function checkVip(username, tier = 'STANDARD', chars = 0, env) {
  await callPgApi('/users/check-quota', {
    username,
    requiredTier: tier,
    requestedChars: chars
  });
}
```

### updateUserUsage函数
```javascript
// 旧版本 (KV)
async function updateUserUsage(username, charCount, env) {
  // KV访问逻辑...
}

// 新版本 (API)
async function updateUserUsage(username, charCount, env) {
  await callPgApi('/users/update-usage', {
    username,
    charCount
  });
}
```

## ⚡ 常用代码片段

### 完整的TTS请求处理
```javascript
export default {
  async fetch(request, env) {
    try {
      // 1. 验证Token
      const token = request.headers.get('Authorization')?.replace('Bearer ', '');
      const username = await verifyToken(token, env);
      
      // 2. 检查配额
      const { text } = await request.json();
      await checkVip(username, 'STANDARD', text.length, env);
      
      // 3. 生成TTS
      const result = await generateTTS(text);
      
      // 4. 更新使用量
      await updateUserUsage(username, text.length, env);
      
      return new Response(JSON.stringify(result));
    } catch (error) {
      return new Response(error.message, { status: 400 });
    }
  }
};
```

### 错误处理模板
```javascript
async function safeApiCall(apiFunction) {
  try {
    return await apiFunction();
  } catch (error) {
    console.error('API调用失败:', error);
    
    if (error.message.includes('配额不足')) {
      return new Response('配额不足，请升级VIP', { status: 403 });
    } else if (error.message.includes('认证失败')) {
      return new Response('认证失败', { status: 401 });
    } else {
      return new Response('服务暂时不可用', { status: 503 });
    }
  }
}
```

### 健康检查
```javascript
async function checkApiHealth(env) {
  try {
    const health = await callPgApi('/health');
    return health.status === 'healthy';
  } catch (error) {
    console.error('健康检查失败:', error);
    return false;
  }
}
```

## 🚨 错误代码速查

| 代码 | 含义 | 处理方式 |
|------|------|----------|
| 4001 | 认证失败 | 检查API_SECRET_TOKEN |
| 4002 | 参数错误 | 检查请求参数 |
| 4003 | 配额不足 | 提示升级VIP |
| 4004 | 用户不存在 | 检查用户名 |
| 5001 | 服务器错误 | 重试或联系支持 |

## 🔧 调试技巧

### 1. 检查认证配置
```javascript
console.log('API Base:', env.PG_API_BASE);
console.log('API Token:', env.API_SECRET_TOKEN?.substring(0, 10) + '...');
```

### 2. 测试API连接
```javascript
// 简单的连接测试
const health = await fetch(`${env.PG_API_BASE}/api/b-backend/health`, {
  headers: { 'Authorization': `Bearer ${env.API_SECRET_TOKEN}` }
});
console.log('连接状态:', health.status);
```

### 3. 详细错误日志
```javascript
try {
  await callPgApi('/users/check-quota', { username: 'test' });
} catch (error) {
  console.error('详细错误:', {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  });
}
```

## 📊 性能优化

### 1. 请求超时
```javascript
const controller = new AbortController();
setTimeout(() => controller.abort(), 10000); // 10秒超时

const response = await fetch(url, {
  ...options,
  signal: controller.signal
});
```

### 2. 重试机制
```javascript
async function retryApiCall(apiCall, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. 批量操作
```javascript
// 避免串行调用，使用Promise.all进行并行处理
const results = await Promise.allSettled([
  checkVip(user1, 'STANDARD', 100, env),
  checkVip(user2, 'PREMIUM', 200, env),
  checkVip(user3, 'STANDARD', 150, env)
]);
```

## 📋 部署检查清单

### 后端服务
- [ ] `API_SECRET_TOKEN` 已配置
- [ ] `ENABLE_B_BACKEND_API=true` 已设置
- [ ] 服务运行在正确端口 (默认3001)
- [ ] 健康检查接口可访问

### Cloudflare Workers
- [ ] `PG_API_BASE` 环境变量已设置
- [ ] `API_SECRET_TOKEN` 环境变量已设置
- [ ] 函数替换已完成
- [ ] 错误处理已实现

### 验证测试
- [ ] 健康检查通过
- [ ] Token验证正常
- [ ] 用户注册同步正常 🆕
- [ ] 密码重置同步正常 🆕
- [ ] 配额检查功能正常
- [ ] 使用量更新正常

## 🆕 新增接口使用示例

### 用户注册同步
```javascript
// A后端验证成功后调用
async function syncUserRegistration(username, passwordHash, email, env) {
  return await callPgApi('/users/register', {
    username,
    passwordHash,
    email,
    createdAt: Date.now()
  });
}

// 使用示例
try {
  const result = await syncUserRegistration('newuser', 'hashed_password', '<EMAIL>', env);
  console.log('用户注册同步成功:', result.userData);
} catch (error) {
  console.error('用户注册同步失败:', error.message);
}
```

### 密码重置同步
```javascript
// A后端验证成功后调用
async function syncPasswordReset(username, newPasswordHash, env) {
  return await callPgApi('/users/reset-password', {
    username,
    newPasswordHash,
    passwordUpdatedAt: Date.now()
  });
}

// 使用示例
try {
  const result = await syncPasswordReset('existinguser', 'new_hashed_password', env);
  console.log('密码重置同步成功:', result.message);
} catch (error) {
  console.error('密码重置同步失败:', error.message);
}
```

### 完整的用户注册流程示例
```javascript
// A后端注册处理函数
async function handleUserRegistration(request, env) {
  const { username, password, email, verificationCode } = await request.json();

  try {
    // 1. 验证邮箱验证码
    await verifyEmailCode(email, verificationCode);

    // 2. 生成密码哈希
    const passwordHash = await hashPassword(password);

    // 3. 同步到B后端PostgreSQL
    const syncResult = await syncUserRegistration(username, passwordHash, email, env);

    // 4. 生成JWT Token
    const token = await generateJWT(username);

    return new Response(JSON.stringify({
      success: true,
      message: '注册成功',
      token,
      userData: syncResult.userData
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
```

---

**💡 提示：将此文档保存为书签，便于开发时快速查阅！**
