#!/usr/bin/env node

/**
 * Cloudflare KV API性能测试脚本
 * 测试KV API的响应时间和网络延迟
 */

require('dotenv').config();

const KV_CONFIG = {
  CF_ACCOUNT_ID: process.env.CF_ACCOUNT_ID,
  CF_API_TOKEN: process.env.CF_API_TOKEN,
  KV_NAMESPACES: {
    USERS: '8341ec47189543b48818f57e9ca4e5e0',
    CARDS: '69d6e32b35dd4a0bb996584ebf3f5b27'
  }
};

// 颜色输出函数
function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color] || ''}${message}${colors.reset}`);
}

// 测试KV API响应时间
async function testKVApiPerformance() {
  if (!KV_CONFIG.CF_ACCOUNT_ID || !KV_CONFIG.CF_API_TOKEN) {
    log('red', '❌ Cloudflare配置缺失');
    return;
  }

  log('blue', '🚀 开始Cloudflare KV API性能测试...\n');

  const tests = [
    {
      name: '获取KV键列表',
      url: `https://api.cloudflare.com/client/v4/accounts/${KV_CONFIG.CF_ACCOUNT_ID}/storage/kv/namespaces/${KV_CONFIG.KV_NAMESPACES.USERS}/keys`,
      method: 'GET'
    },
    {
      name: '写入测试数据',
      url: `https://api.cloudflare.com/client/v4/accounts/${KV_CONFIG.CF_ACCOUNT_ID}/storage/kv/namespaces/${KV_CONFIG.KV_NAMESPACES.USERS}/values/test:performance`,
      method: 'PUT',
      body: JSON.stringify({
        test: true,
        timestamp: Date.now(),
        data: 'performance test data'
      })
    },
    {
      name: '读取测试数据',
      url: `https://api.cloudflare.com/client/v4/accounts/${KV_CONFIG.CF_ACCOUNT_ID}/storage/kv/namespaces/${KV_CONFIG.KV_NAMESPACES.USERS}/values/test:performance`,
      method: 'GET'
    }
  ];

  const results = [];

  for (const test of tests) {
    log('cyan', `📊 测试: ${test.name}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch(test.url, {
        method: test.method,
        headers: {
          'Authorization': `Bearer ${KV_CONFIG.CF_API_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: test.body
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (response.ok) {
        log('green', `   ✅ 成功 - 耗时: ${duration}ms`);
        results.push({ test: test.name, duration, success: true });
      } else {
        log('red', `   ❌ 失败 - HTTP ${response.status} - 耗时: ${duration}ms`);
        results.push({ test: test.name, duration, success: false, status: response.status });
      }
      
    } catch (error) {
      log('red', `   ❌ 错误: ${error.message}`);
      results.push({ test: test.name, duration: 0, success: false, error: error.message });
    }
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 输出测试结果汇总
  log('blue', '\n📈 性能测试结果汇总:');
  log('blue', '================================');
  
  let totalDuration = 0;
  let successCount = 0;
  
  results.forEach(result => {
    if (result.success) {
      log('green', `✅ ${result.test}: ${result.duration}ms`);
      totalDuration += result.duration;
      successCount++;
    } else {
      log('red', `❌ ${result.test}: 失败`);
    }
  });
  
  if (successCount > 0) {
    const avgDuration = Math.round(totalDuration / successCount);
    log('blue', '================================');
    log('cyan', `📊 平均响应时间: ${avgDuration}ms`);
    log('cyan', `📊 总耗时: ${totalDuration}ms`);
    log('cyan', `📊 成功率: ${successCount}/${results.length} (${(successCount/results.length*100).toFixed(1)}%)`);
    
    // 性能评估
    if (avgDuration < 1000) {
      log('green', '🎉 KV API性能良好 (< 1秒)');
    } else if (avgDuration < 3000) {
      log('yellow', '⚠️  KV API性能一般 (1-3秒)');
    } else if (avgDuration < 10000) {
      log('red', '🐌 KV API性能较慢 (3-10秒)');
    } else {
      log('red', '🚨 KV API性能很差 (> 10秒)');
    }
  }
}

// 测试网络连接
async function testNetworkConnectivity() {
  log('blue', '\n🌐 测试网络连接...');
  
  const testUrls = [
    'https://api.cloudflare.com',
    'https://www.cloudflare.com',
    'https://1.1.1.1'
  ];
  
  for (const url of testUrls) {
    try {
      const startTime = Date.now();
      const response = await fetch(url, { method: 'HEAD' });
      const duration = Date.now() - startTime;
      
      if (response.ok) {
        log('green', `✅ ${url}: ${duration}ms`);
      } else {
        log('yellow', `⚠️  ${url}: HTTP ${response.status} - ${duration}ms`);
      }
    } catch (error) {
      log('red', `❌ ${url}: ${error.message}`);
    }
  }
}

// 分析用户注册流程的性能瓶颈
async function analyzeUserRegistrationBottleneck() {
  log('blue', '\n🔍 分析用户注册流程性能瓶颈...');
  
  const steps = [
    {
      name: '1. 参数验证',
      estimatedTime: '< 1ms',
      description: '用户名、邮箱格式验证'
    },
    {
      name: '2. 数据库查重',
      estimatedTime: '1-5ms',
      description: 'PostgreSQL查询用户是否存在'
    },
    {
      name: '3. 数据库插入',
      estimatedTime: '1-5ms', 
      description: 'PostgreSQL插入新用户记录'
    },
    {
      name: '4. KV同步 - 用户数据',
      estimatedTime: '?ms',
      description: 'Cloudflare KV API写入用户数据'
    },
    {
      name: '5. KV同步 - 邮箱映射',
      estimatedTime: '?ms',
      description: 'Cloudflare KV API写入邮箱映射'
    }
  ];
  
  log('cyan', '流程步骤分析:');
  steps.forEach((step, index) => {
    log('white', `   ${step.name}: ${step.estimatedTime} (${step.description})`);
  });
  
  log('yellow', '\n💡 根据日志分析:');
  log('yellow', '   - 总耗时: 10640ms');
  log('yellow', '   - 数据库操作: ~2ms (查重1ms + 插入1ms)');
  log('yellow', '   - KV同步: ~10638ms (占99.98%的时间)');
  log('yellow', '   - 瓶颈: Cloudflare KV API调用');
}

// 主函数
async function main() {
  try {
    await testNetworkConnectivity();
    await testKVApiPerformance();
    await analyzeUserRegistrationBottleneck();
    
    log('blue', '\n🎯 优化建议:');
    log('cyan', '1. 考虑异步KV同步 - 先返回成功响应，后台同步KV');
    log('cyan', '2. 添加KV同步超时控制 - 避免长时间等待');
    log('cyan', '3. 实现KV同步重试机制 - 提高成功率');
    log('cyan', '4. 考虑批量KV操作 - 减少API调用次数');
    log('cyan', '5. 添加KV同步失败降级策略 - 不影响主流程');
    
  } catch (error) {
    log('red', `测试执行失败: ${error.message}`);
  }
}

// 执行测试
if (require.main === module) {
  main().catch(error => {
    log('red', `程序执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testKVApiPerformance, testNetworkConnectivity };
